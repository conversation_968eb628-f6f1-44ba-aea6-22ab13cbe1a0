package com.dep.biguo.mvp.ui.fragment;

import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.databinding.DataBindingUtil;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.GridLayoutManager;

import com.dep.biguo.R;
import com.dep.biguo.bean.ShopBean;
import com.dep.biguo.bean.BannerBean;
import com.dep.biguo.databinding.MallHomeFragmentNewBinding;
import com.dep.biguo.mvp.ui.adapter.MallProductAdapter;
import com.dep.biguo.mvp.ui.adapter.HotProductAdapter;
import com.dep.biguo.mvp.ui.adapter.BannerAdapter;
import com.dep.biguo.mvp.ui.activity.ShopDetailActivity;
import com.dep.biguo.mvp.ui.activity.ShopCartActivity;
import com.dep.biguo.utils.MainAppUtils;
import com.jess.arms.base.BaseFragment;
import com.jess.arms.di.component.AppComponent;
import com.jess.arms.utils.ArmsUtils;
import com.google.android.material.tabs.TabLayout;
import com.biguo.utils.util.AppUtil;
import android.content.res.TypedArray;
import android.view.LayoutInflater;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.view.Gravity;
import android.view.ViewGroup;
import com.biguo.utils.util.DisplayHelper;

import java.util.ArrayList;
import java.util.List;

public class MallHomeNewFragment extends BaseFragment implements View.OnClickListener {

    private static final String TAG = "MallHomeNewFragment";
    
    private MallHomeFragmentNewBinding binding;
    private BannerAdapter bannerAdapter;
    private HotProductAdapter hotProductAdapter;
    private MallProductAdapter allProductAdapter;

    public static MallHomeNewFragment newInstance() {
        return new MallHomeNewFragment();
    }

    @Override
    public void setupFragmentComponent(@NonNull AppComponent appComponent) {
        // DI setup if needed
    }

    @Override
    public View initView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        binding = DataBindingUtil.inflate(inflater, R.layout.mall_home_fragment_new, container, false);
        binding.setOnClickListener(this);
        return binding.getRoot();
    }

    @Override
    public void initData(@Nullable Bundle savedInstanceState) {
        Log.d(TAG, "initData() called");
        setupViews();
        loadMockData();
    }

    private void setupViews() {
        Log.d(TAG, "setupViews() called");

        // 初始化底部导航栏
        initBottomTab();

        // 设置分类标签点击事件
        binding.tabLayoutCategories.addOnTabSelectedListener(new TabLayout.OnTabSelectedListener() {
            @Override
            public void onTabSelected(TabLayout.Tab tab) {
                Log.d(TAG, "Tab selected: " + tab.getText());
                // TODO: 根据选中的标签筛选商品
            }

            @Override
            public void onTabUnselected(TabLayout.Tab tab) {}

            @Override
            public void onTabReselected(TabLayout.Tab tab) {}
        });

        // 设置搜索框点击事件
        binding.tvSearchBar.setOnClickListener(v -> {
            Log.d(TAG, "Search bar clicked");
            // TODO: 打开搜索页面
        });

        // 设置更多按钮点击事件
        binding.ivHotSaleMore.setOnClickListener(v -> {
            Log.d(TAG, "More hot products clicked");
            // TODO: 打开更多热卖商品页面
        });
    }

    /**
     * 初始化底部导航栏 - 使用与主应用相同的设计
     */
    private void initBottomTab() {
        if (getContext() == null) return;

        // 清除TabLayout的点击颜色
        AppUtil.clearTabClickColor(getActivity(), binding.tbMain);

        // 获取主应用的tab配置
        String[] mainTabTitles = getResources().getStringArray(R.array.main_tab_text);
        TypedArray mainTabIcons = getResources().obtainTypedArray(R.array.main_tab_icon);

        // 添加所有tab
        for (int i = 0; i < mainTabTitles.length; i++) {
            TabLayout.Tab tab = binding.tbMain.newTab();
            initTabItem(tab, mainTabTitles[i], mainTabIcons.getDrawable(i), i);
            binding.tbMain.addTab(tab);
        }

        // 设置商城tab为选中状态（索引为2）
        binding.tbMain.selectTab(binding.tbMain.getTabAt(2));

        // 回收TypedArray
        mainTabIcons.recycle();
    }

    /**
     * 初始化单个Tab项 - 与主应用保持一致
     */
    private void initTabItem(TabLayout.Tab tab, String title, android.graphics.drawable.Drawable icon, int position) {
        if (getContext() == null) return;

        View tabView = LayoutInflater.from(getContext()).inflate(R.layout.main_tab_item, null);

        ImageView ivIcon = tabView.findViewById(R.id.ivIcon);
        TextView tvName = tabView.findViewById(R.id.tvName);

        ivIcon.setImageDrawable(icon);
        tvName.setText(title);

        tab.setCustomView(tabView);
        tabView.setLayoutParams(new LinearLayout.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, DisplayHelper.dp2px(getContext(), 44)));
        LinearLayout linearLayout = (LinearLayout) tabView.getParent();
        linearLayout.setPadding(0, 0, 0, 0);
        linearLayout.setGravity(Gravity.CENTER);
    }

    private void loadMockData() {
        Log.d(TAG, "loadMockData() called");

        try {
            // 模拟 Banner 数据
            List<BannerBean> banners = new ArrayList<>();
            for (int i = 0; i < 3; i++) {
                BannerBean banner = new BannerBean();
                banner.setId(i + 1);
                banner.setTitle("限时优惠活动 " + (i + 1));
                banner.setImageUrl(""); // 空URL将使用默认背景
                banner.setLinkUrl("https://example.com/banner" + (i + 1));
                banner.setType(1);
                banners.add(banner);
            }
            Log.d(TAG, "Created " + banners.size() + " banners");

            // 设置 Banner 适配器
            bannerAdapter = new BannerAdapter(banners);
            LinearLayoutManager bannerLayoutManager = new LinearLayoutManager(getContext(), LinearLayoutManager.HORIZONTAL, false);
            binding.rvBanner.setLayoutManager(bannerLayoutManager);
            binding.rvBanner.setAdapter(bannerAdapter);
            Log.d(TAG, "Banner RecyclerView setup complete");

            // Banner 点击事件
            bannerAdapter.setOnItemClickListener((adapter, view, position) -> {
                Log.d(TAG, "Banner clicked at position: " + position);
                BannerBean banner = bannerAdapter.getItem(position);
                if (banner != null) {
                    // TODO: 处理 Banner 点击事件，可以根据 type 和 linkUrl 进行不同的跳转
                    Log.d(TAG, "Banner clicked: " + banner.getTitle());
                }
            });
            // 模拟热卖商品数据
            List<ShopBean> hotProducts = new ArrayList<>();
            for (int i = 0; i < 3; i++) {
                ShopBean bean = new ShopBean();
                bean.setId(i + 1);
                bean.setName("笔果AI学习机");
                bean.setPrice("2500.00");
                bean.setPreferential_price("3000.00");
                bean.setSales_volume(1000 + i * 500);
                hotProducts.add(bean);
            }
            Log.d(TAG, "Created " + hotProducts.size() + " hot products");

            // 设置热卖商品适配器
            hotProductAdapter = new HotProductAdapter(hotProducts);
            LinearLayoutManager hotLayoutManager = new LinearLayoutManager(getContext(), LinearLayoutManager.HORIZONTAL, false);
            binding.rvHotSale.setLayoutManager(hotLayoutManager);
            binding.rvHotSale.setAdapter(hotProductAdapter);
            Log.d(TAG, "Hot products RecyclerView setup complete");

            // 模拟全部商品数据
            List<ShopBean> allProducts = new ArrayList<>();
            for (int i = 0; i < 6; i++) {
                ShopBean bean = new ShopBean();
                bean.setId(i + 10);
                bean.setName("这里是商品名称最多展示两行文字");
                bean.setPrice("128.00");
                bean.setPreferential_price("150.00");
                bean.setSales_volume(8000 + i * 200);
                bean.setTag("笔果");
                allProducts.add(bean);
            }
            Log.d(TAG, "Created " + allProducts.size() + " all products");

            // 设置全部商品适配器
            allProductAdapter = new MallProductAdapter(allProducts);
            GridLayoutManager allLayoutManager = new GridLayoutManager(getContext(), 2);
            binding.rvAllProducts.setLayoutManager(allLayoutManager);
            binding.rvAllProducts.setAdapter(allProductAdapter);
            Log.d(TAG, "All products RecyclerView setup complete");

            // 设置点击事件
            hotProductAdapter.setOnItemClickListener((adapter, view, position) -> {
                Log.d(TAG, "Hot product clicked at position: " + position);
                ShopBean item = hotProductAdapter.getItem(position);
                if (item != null) {
                    ShopDetailActivity.Start(getContext(), item.getId());
                }
            });

            allProductAdapter.setOnItemClickListener((adapter, view, position) -> {
                Log.d(TAG, "All product clicked at position: " + position);
                ShopBean item = allProductAdapter.getItem(position);
                if (item != null) {
                    ShopDetailActivity.Start(getContext(), item.getId());
                }
            });

            // 更新购物车角标
            updateCartBadge(10);

        } catch (Exception e) {
            Log.e(TAG, "Error in loadMockData(): " + e.getMessage(), e);
        }
    }

    private void updateCartBadge(int count) {
        TextView cartBadge = binding.tvCartBadge;
        if (count > 0) {
            cartBadge.setVisibility(View.VISIBLE);
            cartBadge.setText(String.valueOf(count));
        } else {
            cartBadge.setVisibility(View.GONE);
        }
    }

    @Override
    public void onClick(View v) {
        int id = v.getId();

        if (id == R.id.fl_cart_container) {
            // 购物车
            if (!MainAppUtils.checkLogin(getContext())) return;
            ArmsUtils.startActivity(ShopCartActivity.class);
            Log.d(TAG, "Cart clicked");
        }
    }

    @Override
    public void setData(@Nullable Object data) {
        // 可以接收外部传入的数据
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        binding = null;
    }
}
