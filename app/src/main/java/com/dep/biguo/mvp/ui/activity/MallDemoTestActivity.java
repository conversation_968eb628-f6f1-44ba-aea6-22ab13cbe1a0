package com.dep.biguo.mvp.ui.activity;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.Button;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import com.dep.biguo.R;

/**
 * 商城演示测试页面
 * 提供一个简单的入口来测试新的商城设计
 */
public class MallDemoTestActivity extends AppCompatActivity {

    public static void start(Context context) {
        Intent intent = new Intent(context, MallDemoTestActivity.class);
        context.startActivity(intent);
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_mall_demo_test);
        
        // 设置标题
        if (getSupportActionBar() != null) {
            getSupportActionBar().setTitle("商城演示测试");
            getSupportActionBar().setDisplayHomeAsUpEnabled(true);
        }
        
        // 设置按钮点击事件
        Button btnOpenMallDemo = findViewById(R.id.btn_open_mall_demo);
        btnOpenMallDemo.setOnClickListener(v -> {
            MallDemoActivity.start(this);
        });
    }

    @Override
    public boolean onSupportNavigateUp() {
        onBackPressed();
        return true;
    }
}
