<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <!-- 定义点击事件的处理器变量 -->
        <variable
            name="onClickListener"
            type="com.dep.biguo.mvp.ui.fragment.MallHomeNewFragment" />
    </data>

    <!-- 使用CoordinatorLayout作为根布局，便于未来扩展，例如实现复杂的滚动效果 -->
    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white"
        android:fitsSystemWindows="false">

        <!-- 主要内容区域，使用NestedScrollView使其可滚动 -->
        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="0dp"
            android:fitsSystemWindows="true">

            <!-- 使用ConstraintLayout来组织所有可滚动的内容 -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingBottom="16dp">

                <!-- 1. 顶部标题 -->
                <TextView
                    android:id="@+id/tv_title_mall"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:paddingTop="8dp"
                    android:text="商城"
                    android:textColor="@android:color/black"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <!-- 2. 搜索框 -->
                <TextView
                    android:id="@+id/tv_search_bar"
                    android:layout_width="0dp"
                    android:layout_height="40dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="12dp"
                    android:layout_marginEnd="16dp"
                    android:background="@drawable/bg_search_bar"
                    android:drawableStart="@drawable/icon_search"
                    android:drawablePadding="8dp"
                    android:gravity="center_vertical"
                    android:paddingStart="12dp"
                    android:paddingEnd="12dp"
                    android:text="搜索关键字，查询题目"
                    android:textColor="@android:color/darker_gray"
                    app:layout_constraintEnd_toStartOf="@+id/fl_cart_container"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_title_mall" />

                <!-- 3. 购物车图标及角标 -->
                <FrameLayout
                    android:id="@+id/fl_cart_container"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="16dp"
                    android:onClick="@{onClickListener::onClick}"
                    app:layout_constraintBottom_toBottomOf="@id/tv_search_bar"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="@id/tv_search_bar">

                    <ImageView
                        android:id="@+id/iv_cart"
                        android:layout_width="28dp"
                        android:layout_height="28dp"
                        android:src="@drawable/icon_shopping_cart" />

                    <TextView
                        android:id="@+id/tv_cart_badge"
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:layout_gravity="top|end"
                        android:background="@drawable/bg_circle_red"
                        android:gravity="center"
                        android:text="10"
                        android:textColor="@android:color/white"
                        android:textSize="10sp"
                        tools:ignore="SmallSp" />
                </FrameLayout>

                <!-- 4. 广告横幅 - RecyclerView Banner -->
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_banner"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="16dp"
                    android:layout_marginEnd="16dp"
                    android:clipToPadding="false"
                    android:orientation="horizontal"
                    app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                    app:layout_constraintDimensionRatio="H,2:1"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/tv_search_bar"
                    tools:itemCount="3"
                    tools:listitem="@layout/item_banner" />

                <!-- 5. 分类导航 -->
                <com.google.android.material.tabs.TabLayout
                    android:id="@+id/tab_layout_categories"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    app:layout_constraintTop_toBottomOf="@id/rv_banner"
                    app:tabIndicator="@drawable/bg_tab_indicator"
                    app:tabIndicatorHeight="3dp"
                    app:tabMode="fixed"
                    app:tabTextColor="@android:color/black"
                    app:tabSelectedTextColor="#D53E43">

                    <com.google.android.material.tabs.TabItem
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="学习工具" />
                    <com.google.android.material.tabs.TabItem
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="二手教材" />
                    <com.google.android.material.tabs.TabItem
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="助农项目" />
                    <com.google.android.material.tabs.TabItem
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="周边产品" />
                </com.google.android.material.tabs.TabLayout>

                <!-- 6. 热卖商品区域 - 使用优化后的布局 -->
                <!-- 热卖商品背景包裹容器 -->
                <FrameLayout
                    android:id="@+id/fl_hot_sale_background"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="16dp"
                    android:layout_marginEnd="16dp"
                    android:background="@drawable/bg_hot_sale_gradient"
                    android:padding="16dp"
                    app:layout_constraintTop_toBottomOf="@id/tab_layout_categories">

                    <LinearLayout
                        android:id="@+id/ll_hot_sale_section"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                    <!--
                        1. 标题区域
                        使用 ConstraintLayout 灵活地定位标题内部的元素
                    -->
                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content">

                        <!-- 热卖商品标题容器，使用 bg_hot_product 背景 -->
                        <LinearLayout
                            android:id="@+id/ll_hot_sale_title"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:background="@drawable/bg_hot_produt"
                            android:gravity="center_vertical"
                            android:orientation="horizontal"
                            android:paddingStart="12dp"
                            android:paddingTop="8dp"
                            android:paddingEnd="12dp"
                            android:paddingBottom="8dp"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent">


                        </LinearLayout>

                        <!-- 分页指示器 -->
                        <LinearLayout
                            android:id="@+id/ll_page_indicators"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            app:layout_constraintBottom_toBottomOf="@id/ll_hot_sale_title"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="@id/ll_hot_sale_title">

                            <!-- 第1页指示器 -->
                            <View
                                android:id="@+id/indicator_1"
                                android:layout_width="12dp"
                                android:layout_height="3dp"
                                android:layout_marginEnd="4dp"
                                android:background="@drawable/bg_indicator_selected" />

                            <!-- 第2页指示器 -->
                            <View
                                android:id="@+id/indicator_2"
                                android:layout_width="6dp"
                                android:layout_height="6dp"
                                android:layout_marginEnd="4dp"
                                android:background="@drawable/bg_indicator_unselected" />

                            <!-- 第3页指示器 -->
                            <View
                                android:id="@+id/indicator_3"
                                android:layout_width="6dp"
                                android:layout_height="6dp"
                                android:layout_marginEnd="4dp"
                                android:background="@drawable/bg_indicator_unselected" />

                            <!-- 第4页指示器 -->
                            <View
                                android:id="@+id/indicator_4"
                                android:layout_width="6dp"
                                android:layout_height="6dp"
                                android:background="@drawable/bg_indicator_unselected" />

                        </LinearLayout>

                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <!--
                        2. 热卖商品 - 横向列表
                    -->
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_hot_sale"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="16dp"
                        android:clipToPadding="false"
                        android:orientation="horizontal"
                        android:paddingEnd="8dp"
                        app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                        tools:itemCount="3"
                        tools:listitem="@layout/item_product_hot" />

                    </LinearLayout>

                </FrameLayout>

                <!-- 7. 全部商品 - 标题部分 -->
                <TextView
                    android:id="@+id/tv_all_products_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="24dp"
                    android:text="全部商品"
                    android:textColor="@android:color/black"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/fl_hot_sale_background" />

                <!-- 8. 全部商品 - 网格列表 -->
                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_all_products"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:clipToPadding="false"
                    android:paddingStart="12dp"
                    android:paddingEnd="12dp"
                    app:layoutManager="androidx.recyclerview.widget.GridLayoutManager"
                    app:layout_constraintTop_toBottomOf="@id/tv_all_products_title"
                    app:spanCount="2"
                    tools:itemCount="4"
                    tools:listitem="@layout/item_mall_product" />

            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.core.widget.NestedScrollView>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

</layout>
