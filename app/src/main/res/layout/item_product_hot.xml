<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="110dp"
    android:layout_height="153dp"
    android:layout_marginEnd="8dp"
    android:background="@drawable/bg_round_8_white_stroke"
    android:orientation="vertical">

        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="94dp"
                app:cardCornerRadius="5dp"
                android:background="@drawable/solide"
                app:cardElevation="2dp">

                <ImageView
                    android:id="@+id/iv_product_image"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginVertical="22dp"
                    android:layout_marginHorizontal="10dp"
                    android:scaleType="centerCrop"
                    tools:src="@tools:sample/backgrounds/scenic" />

            </androidx.cardview.widget.CardView>

            <TextView
                android:id="@+id/tv_sales_rank"
                android:layout_width="wrap_content"
                android:layout_height="14dp"
                android:background="@drawable/bg_top_label"
                android:paddingStart="8dp"
                android:paddingEnd="8dp"
                android:textColor="@color/white"
                android:textSize="8sp"
                tools:text="月销量TOP1" />
        </FrameLayout>

        <TextView
            android:id="@+id/tv_product_name"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:layout_marginTop="6dp"
            android:layout_marginEnd="4dp"
            android:ellipsize="end"
            android:maxLines="1"
            android:textColor="@android:color/black"
            android:textSize="12sp"
            tools:text="笔果AI学习机" />

        <TextView
            android:id="@+id/tv_product_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:layout_marginTop="2dp"
            android:textColor="#F44336"
            android:textSize="14sp"
            android:textStyle="bold"
            tools:text="¥2500.00" />
</LinearLayout>
