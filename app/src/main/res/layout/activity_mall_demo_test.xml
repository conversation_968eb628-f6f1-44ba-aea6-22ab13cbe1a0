<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    android:gravity="center"
    android:background="@color/white">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="商城演示测试"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="@android:color/black"
        android:layout_marginBottom="32dp" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="点击下方按钮查看重写后的商城首页效果"
        android:textSize="16sp"
        android:textColor="@android:color/darker_gray"
        android:layout_marginBottom="32dp"
        android:gravity="center" />

    <Button
        android:id="@+id/btn_open_mall_demo"
        android:layout_width="200dp"
        android:layout_height="wrap_content"
        android:text="打开商城演示"
        android:textSize="16sp"
        android:background="@drawable/bg_round_10_theme"
        android:textColor="@color/white"
        android:layout_marginBottom="16dp" />

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="✨ 新特性："
        android:textSize="14sp"
        android:textStyle="bold"
        android:textColor="@android:color/black"
        android:layout_marginTop="32dp"
        android:layout_marginBottom="8dp" />

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="• Material Design 3 设计风格\n• 响应式布局和流畅动画\n• 热卖商品横向滚动展示\n• 商品网格布局优化\n• 购物车角标提醒\n• 与主应用一致的底部导航"
        android:textSize="14sp"
        android:textColor="@android:color/darker_gray"
        android:lineSpacingExtra="4dp" />

</LinearLayout>
